package config

import (
	"github.com/urfave/cli/v2"
	"wallet-sign-go/flags"
)

type Config struct {
	Host                            string
	Port                            int
	DataLocation                    string
	MaxExportKeyPairCountPerRequest uint64
	AesKey                          string
	BearerToken                     string
}

func NewConfig(ctx *cli.Context) *Config {
	return &Config{
		Host:                            ctx.String(flags.HostFlag.Name),
		Port:                            ctx.Int(flags.PortFlag.Name),
		DataLocation:                    ctx.String(flags.DataLocation.Name),
		MaxExportKeyPairCountPerRequest: ctx.Uint64(flags.MaxExportKeyPairCountPerRequest.Name),
		AesKey:                          ctx.String(flags.AesKey.Name),
		BearerToken:                     ctx.String(flags.BearerToken.Name),
	}
}
