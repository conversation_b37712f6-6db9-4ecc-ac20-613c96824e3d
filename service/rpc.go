package service

import (
	"context"
	"github.com/ethereum/go-ethereum/log"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/reflection"
	"google.golang.org/grpc/status"
	"net"
	"strconv"
	"wallet-sign-go/config"
	"wallet-sign-go/database"
	pb_wallet "wallet-sign-go/protobuf/wallet"
	"wallet-sign-go/ssm"
)

type RpcServer struct {
	config *config.Config
	db     *database.Database
	pb_wallet.UnimplementedWalletServiceServer
}

func (r *RpcServer) getSigner(cryptoType pb_wallet.ECryptoType) ssm.ISsm {
	switch cryptoType {
	case pb_wallet.ECryptoType_EDDSA:
		return ssm.EdDSA
	case pb_wallet.ECryptoType_ECDSA:
		return ssm.EcDSA
	default:
		return nil
	}
}

func (r *RpcServer) GetSupportSignWay(ctx context.Context, request *pb_wallet.GetSupportSignWayRequest) (*pb_wallet.GetSupportSignWayResponse, error) {
	return &pb_wallet.GetSupportSignWayResponse{
		Code: pb_wallet.EReturnCode_SUCCESS,
		Msg:  "",
		Data: []string{
			pb_wallet.ECryptoType_ECDSA.String(),
			pb_wallet.ECryptoType_EDDSA.String(),
		},
	}, nil
}

func (r *RpcServer) ExportPublicKeyList(ctx context.Context, request *pb_wallet.ExportPublicKeyListRequest) (*pb_wallet.ExportPublicKeyListResponse, error) {
	if request.Count > r.config.MaxExportKeyPairCountPerRequest {
		return &pb_wallet.ExportPublicKeyListResponse{
			Code: pb_wallet.EReturnCode_ERROR,
			Msg:  "count is too large",
		}, nil
	}

	var retKeypairList = make([]*pb_wallet.PublicKey, 0, request.Count)

	var keypairList = make([]*ssm.Keypair, 0, request.Count)
	s := r.getSigner(request.Type)
	for i := 0; uint64(i) < request.Count; i++ {
		keypair, err := s.GenerateKeyPair()
		if err != nil {
			log.Error("An error occurred while generating keypair", "err", err)
			return &pb_wallet.ExportPublicKeyListResponse{
				Code: pb_wallet.EReturnCode_ERROR,
				Msg:  "An error occurred while generating keypair",
			}, err
		}

		keypairList = append(keypairList, keypair)
		retKeypairList = append(retKeypairList, &pb_wallet.PublicKey{
			PublicKey:           keypair.PublicKey,
			CompressedPublicKey: keypair.CompressedPublicKey,
		})
	}

	for _, keypair := range keypairList {
		err := r.db.Store(keypair.PublicKey, keypair.PrivateKey)
		if err != nil {
			return nil, err
		}
	}

	return &pb_wallet.ExportPublicKeyListResponse{
		Code: pb_wallet.EReturnCode_SUCCESS,
		Msg:  "",
		Data: retKeypairList,
	}, nil
}

func (r *RpcServer) SignTxMessage(ctx context.Context, request *pb_wallet.SignTxMessageRequest) (*pb_wallet.SignTxMessageResponse, error) {
	privateKey := r.db.Get(request.PublicKey)
	if privateKey == "" {
		return &pb_wallet.SignTxMessageResponse{
			Code: pb_wallet.EReturnCode_ERROR,
			Msg:  "public key not found",
		}, nil
	}

	signedMessage, err := r.getSigner(request.Type).Sign(privateKey, request.MessageHash)

	if err != nil {
		log.Error("An error occurred while signing the message", "err", err.Error())
		return &pb_wallet.SignTxMessageResponse{
			Code: pb_wallet.EReturnCode_ERROR,
			Msg:  "An error occurred while signing the message",
			Data: signedMessage,
		}, nil
	}

	return &pb_wallet.SignTxMessageResponse{
		Code: pb_wallet.EReturnCode_SUCCESS,
		Msg:  "",
		Data: signedMessage,
	}, nil
}

func (r *RpcServer) SignTxMessageInBatches(ctx context.Context, request *pb_wallet.SignTxMessageInBatchesRequest) (*pb_wallet.SignTxMessageInBatchesResponse, error) {
	resList := make([]*pb_wallet.SignTxMessageResponse, len(request.List))
	retRes := &pb_wallet.SignTxMessageInBatchesResponse{
		Code: pb_wallet.EReturnCode_ERROR,
		Msg:  "",
		Data: nil,
	}

	hasError := false
	for i, x := range request.List {
		res, _ := r.SignTxMessage(ctx, x)
		resList[i] = res
		hasError = hasError || res.Code != pb_wallet.EReturnCode_SUCCESS
	}

	if !hasError {
		retRes.Code = pb_wallet.EReturnCode_SUCCESS
	}

	retRes.Data = resList

	return retRes, nil
}

func (r *RpcServer) checkAuth(ctx context.Context) error {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return status.Error(codes.Unauthenticated, "missing metadata")
	}

	tokens := md.Get("authorization")
	if len(tokens) == 0 {
		return status.Error(codes.Unauthenticated, "missing authorization token")
	}

	token := tokens[0]
	if token != "Bearer "+r.config.BearerToken {
		return status.Error(codes.Unauthenticated, "invalid token")
	}

	return nil
}

func (r *RpcServer) unaryServerInterceptor(ctx context.Context, req any, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp any, err error) {
	if err := r.checkAuth(ctx); err != nil {
		return nil, err
	}

	return handler(ctx, req)
}

func (r *RpcServer) Start() error {
	log.Info("Starting RPC Server")
	lis, err := net.Listen("tcp", ":"+strconv.Itoa(r.config.Port))
	if err != nil {
		return err
	}

	// 创建gRPC服务器，添加拦截器
	gServer := grpc.NewServer(grpc.UnaryInterceptor(r.unaryServerInterceptor))

	// 注册服务
	pb_wallet.RegisterWalletServiceServer(gServer, r)

	// 启用 gRPC 反射服务
	// 服务发现 - 让客户端能够动态发现服务器上有哪些 gRPC 服务和方法
	// 元数据查询 - 客户端可以查询服务的 protobuf 定义、方法签名等信息
	// 动态调用 - 无需预先知道 .proto 文件就能调用服务
	reflection.Register(gServer)

	log.Info("RPC Server is running on :" + lis.Addr().String())

	if err := gServer.Serve(lis); err != nil {
		return err
	}

	return nil
}

func NewRpcServer(config *config.Config) (*RpcServer, error) {
	db, err := database.NewDB(config)
	if err != nil {
		return nil, err
	}

	return &RpcServer{
		config: config,
		db:     db,
	}, nil
}
