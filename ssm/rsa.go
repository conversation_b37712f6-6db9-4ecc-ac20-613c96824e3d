package ssm

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/hex"
)

type RSASigner struct {
}

func (R RSASigner) GenerateKeyPair() (*Keypair, error) {
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return nil, err
	}

	publicKey := &privateKey.PublicKey

	privateKeyBytes := x509.MarshalPKCS1PrivateKey(privateKey)
	publicKeyBytes, err := x509.MarshalPKIXPublicKey(publicKey)
	if err != nil {
		return nil, err
	}

	publicKeyBase64 := base64.StdEncoding.EncodeToString(publicKeyBytes)
	return &Keypair{
		PrivateKey:          base64.StdEncoding.EncodeToString(privateKeyBytes),
		PublicKey:           publicKeyBase64,
		CompressedPublicKey: publicKeyBase64, // RSA 没有压缩公钥概念，使用相同值
	}, nil
}

func (R RSASigner) Sign(privateKey string, messageHashHex string) (string, error) {
	privateKeyBytes, err := base64.StdEncoding.DecodeString(privateKey)
	if err != nil {
		return "", err
	}

	priv, err := x509.ParsePKCS1PrivateKey(privateKeyBytes)
	if err != nil {
		return "", err
	}

	// 将十六进制字符串转换为字节数组（与 ECDSA 和 EdDSA 保持一致）
	messageBytes, err := hex.DecodeString(messageHashHex)
	if err != nil {
		return "", err
	}

	// 对消息进行 SHA256 哈希
	hash := sha256.Sum256(messageBytes)

	// 使用 SHA256 哈希算法进行签名
	signature, err := rsa.SignPKCS1v15(rand.Reader, priv, crypto.SHA256, hash[:])
	if err != nil {
		return "", err
	}

	return base64.StdEncoding.EncodeToString(signature), nil
}
