package ssm

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
)

type RSASigner struct {
}

func (R RSASigner) GenerateKeyPair() (*Keypair, error) {
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return nil, err
	}

	publicKey := &privateKey.PublicKey

	privateKeyBytes := x509.MarshalPKCS1PrivateKey(privateKey)
	publicKeyBytes, err := x509.MarshalPKIXPublicKey(publicKey)
	if err != nil {
		return nil, err
	}

	return &Keypair{
		PrivateKey: base64.StdEncoding.EncodeToString(privateKeyBytes),
		PublicKey:  base64.StdEncoding.EncodeToString(publicKeyBytes),
	}, nil
}

func (R RSASigner) Sign(privateKey string, messageHash string) (string, error) {
	privateKeyBytes, err := base64.StdEncoding.DecodeString(privateKey)
	if err != nil {
		return "", err
	}

	priv, err := x509.ParsePKCS1PrivateKey(privateKeyBytes)
	if err != nil {
		return "", err
	}

	hashedMessage := []byte(messageHash)

	signature, err := rsa.SignPKCS1v15(rand.Reader, priv, crypto.Hash(0), hashedMessage)
	if err != nil {
		return "", err
	}

	return base64.StdEncoding.EncodeToString(signature), nil

}
