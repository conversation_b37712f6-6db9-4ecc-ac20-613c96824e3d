package ssm_test

import (
	"github.com/ethereum/go-ethereum/log"
	"github.com/stretchr/testify/assert"
	"os"
	"testing"
	"wallet-sign-go/ssm"
)

func TestRSASigner_GenerateKeyPair(t *testing.T) {
	log.SetDefault(log.NewLogger(log.NewTerminalHandlerWithLevel(os.Stderr, log.LevelInfo, true)))

	pair, err := ssm.Rsa.GenerateKeyPair()
	assert.NoError(t, err)

	log.Info(
		"GenerateKeyPair",
		"privateKey", pair.PrivateKey,
		"publicKey", pair.PublicKey,
		"compressedPublicKey", pair.CompressedPublicKey,
	)

	assert.IsType(t, pair.PublicKey, string(""))
	assert.IsType(t, pair.CompressedPublicKey, string(""))
	assert.IsType(t, pair.PrivateKey, string(""))
	// RSA 没有压缩公钥的概念，所以应该是空的或者与公钥相同
	assert.Equal(t, pair.<PERSON><PERSON><PERSON>, pair.CompressedPublicKey)
}

func TestRSASigner_Sign(t *testing.T) {
	log.SetDefault(log.NewLogger(log.NewTerminalHandlerWithLevel(os.Stdout, log.LevelInfo, true)))

	// 首先生成一个密钥对
	pair, err := ssm.Rsa.GenerateKeyPair()
	assert.NoError(t, err)

	// 使用十六进制消息哈希（与 ECDSA 和 EdDSA 保持一致）
	messageHex := "123456"
	signedMessage, err := ssm.Rsa.Sign(pair.PrivateKey, messageHex)
	assert.NoError(t, err)
	assert.IsType(t, signedMessage, string(""))
	assert.NotEmpty(t, signedMessage)

	log.Info(
		"Sign",
		"messageHex", messageHex,
		"signedMessage", signedMessage,
	)
}
