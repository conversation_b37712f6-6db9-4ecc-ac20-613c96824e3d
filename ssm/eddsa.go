package ssm

import (
	"crypto/ed25519"
	"crypto/rand"
	"github.com/status-im/keycard-go/hexutils"
)

type EddsaSigner struct {
}

func (e *EddsaSigner) Sign(privateKeyHex string, messageHex string) (string, error) {
	privateKeyBytes := hexutils.HexToBytes(privateKeyHex)
	messageBytes := hexutils.HexToBytes(messageHex)
	signature := ed25519.Sign(privateKeyBytes, messageBytes)
	return hexutils.BytesToHex(signature), nil
}

func (e *EddsaSigner) GenerateKeyPair() (*Keypair, error) {
	publicKey, privateKey, err := ed25519.GenerateKey(rand.Reader)
	if err != nil {
		return nil, err
	}

	return &Keypair{
		PublicKey:           hexutils.BytesToHex(publicKey),
		CompressedPublicKey: hexutils.BytesToHex(publicKey),
		PrivateKey:          hexutils.BytesToHex(privateKey),
	}, nil
}
