package ssm_test

import (
	"github.com/ethereum/go-ethereum/log"
	"github.com/stretchr/testify/assert"
	"os"
	"testing"
	"wallet-sign-go/ssm"
)

func TestEcDSASigner_Sign(t *testing.T) {
	log.SetDefault(log.NewLogger(log.NewTerminalHandlerWithLevel(os.Stdout, log.LevelInfo, true)))

	privateKey := "10581B0613ED77C5D5519019BCC5F9F88C89CA1AD46FD8A98C2EB47D875CAB0D"
	messageHex := "123"
	signedMessage, err := ssm.Ecdsa.Sign(privateKey, messageHex)
	assert.NoError(t, err)
	assert.IsType(t, signedMessage, string(""))
	log.Info(
		"Sign",
		"signedMessage", signedMessage,
	)
}

func TestEcDSASigner_GenerateKeyPair(t *testing.T) {
	log.SetDefault(log.NewLogger(log.NewTerminalHandlerWithLevel(os.Stderr, log.LevelInfo, true)))

	pair, err := ssm.Ecdsa.GenerateKeyPair()
	assert.NoError(t, err)

	log.Info(
		"GenerateKeyPair",
		"privateKey", pair.PrivateKey,
		"publicKey", pair.PublicKey,
		"compressedPublicKey", pair.CompressedPublicKey,
	)

	assert.IsType(t, pair.PublicKey, string(""))
	assert.IsType(t, pair.CompressedPublicKey, string(""))
	assert.IsType(t, pair.PrivateKey, string(""))
	assert.NotEqual(t, pair.PublicKey, pair.CompressedPublicKey)
}

func TestEcDSASigner_VerifySignature(t *testing.T) {
	log.SetDefault(log.NewLogger(log.NewTerminalHandlerWithLevel(os.Stdout, log.LevelInfo, true)))

	// 生成密钥对
	pair, err := ssm.Ecdsa.GenerateKeyPair()
	assert.NoError(t, err)

	messageHex := "123456"

	// 签名
	signature, err := ssm.Ecdsa.Sign(pair.PrivateKey, messageHex)
	assert.NoError(t, err)

	// 验证签名（使用未压缩公钥）
	isValid, err := ssm.Ecdsa.VerifySignature(pair.PublicKey, messageHex, signature)
	assert.NoError(t, err)
	assert.True(t, isValid)

	// 验证签名（使用压缩公钥）
	isValidCompressed, err := ssm.Ecdsa.VerifySignature(pair.CompressedPublicKey, messageHex, signature)
	assert.NoError(t, err)
	assert.True(t, isValidCompressed)

	// 验证错误的签名
	wrongSignature := "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef00"
	isValidWrong, err := ssm.Ecdsa.VerifySignature(pair.PublicKey, messageHex, wrongSignature)
	assert.NoError(t, err)
	assert.False(t, isValidWrong)

	log.Info(
		"VerifySignature",
		"messageHex", messageHex,
		"signature", signature,
		"isValid", isValid,
		"isValidCompressed", isValidCompressed,
		"isValidWrong", isValidWrong,
	)
}
