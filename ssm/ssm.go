package ssm

import pb_wallet "wallet-sign-go/protobuf/wallet"

var ecdsa *EcdsaSigner = &EcdsaSigner{}
var eddsa *EddsaSigner = &EddsaSigner{}
var rsa *RSASigner = &RSASigner{}

func Factory(signWay pb_wallet.ECryptoType) ISsm {
	switch signWay {
	case pb_wallet.ECryptoType_ECDSA:

		return ecdsa
	case pb_wallet.ECryptoType_EDDSA:
		return eddsa
	case pb_wallet.ECryptoType_RSA:
		return rsa
	default:
		return nil
	}
}
