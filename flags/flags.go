package flags

import "github.com/urfave/cli/v2"

func withPrefixEnvVars(name string) []string {
	return []string{"WALLET_SIGN_GO_" + name}
}

var (
	HostFlag = &cli.StringFlag{
		Name:     "host",
		Usage:    "rpc host",
		EnvVars:  withPrefixEnvVars("HOST"),
		Required: true,
	}
	PortFlag = &cli.IntFlag{
		Name:     "port",
		Usage:    "rpc port",
		EnvVars:  withPrefixEnvVars("PORT"),
		Required: true,
	}
	DataLocation = &cli.StringFlag{
		Name:     "data-location",
		Usage:    "the location data place",
		EnvVars:  withPrefixEnvVars("DATA_LOCATION"),
		Required: true,
	}
	MaxExportKeyPairCountPerRequest = &cli.IntFlag{
		Name:     "max-export-key-pair-count-per-request",
		Usage:    "the max count of keypair export per request",
		EnvVars:  withPrefixEnvVars("MAX_EXPORT_KEY_PAIR_COUNT_PER_REQUEST"),
		Required: true,
	}
	AesKey = &cli.StringFlag{
		Name:     "aes-key",
		Usage:    "the key used to encrypt private key",
		EnvVars:  withPrefixEnvVars("AES_KEY"),
		Required: true,
	}
	BearerToken = &cli.StringFlag{
		Name:     "bearer-token",
		Usage:    "the token used to auth",
		EnvVars:  withPrefixEnvVars("BEARER_TOKEN"),
		Required: true,
	}
)

var AllFlags = []cli.Flag{
	HostFlag,
	PortFlag,
	DataLocation,
	MaxExportKeyPairCountPerRequest,
	AesKey,
}
