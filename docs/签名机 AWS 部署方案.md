### 签名机 AWS 部署方案

## 名词解释

- TEE（Trusted Execution Environment） 是一个安全、隔离的处理区域。放进这个保险箱里的代码和数据，即使是服务器的操作系统（OS）、虚拟机监控器（Hypervisor），甚至是云服务提供商（如AWS的管理员），都无法访问或窥探。他可以通过一个 VSOCK 通道（一个端口）与一个划分了 Enclave 隔离计算环境的 EC2 实例进行通信（和它通信的客户端可以多个）
- KMS（Key Management Service）是一种云服务或系统，专门用于创建、存储、管理和控制加密密钥的生命周期。它提供集中化的密钥管理解决方案。
- cloudHSM：是一个专用密钥管理服务，比较贵，通常用于保存热钱包的私钥
- S3：对象存储服务
- cloudKMS：谷歌的专用密钥管理服务

## 流程

这套代码会部署到 TEE 环境下，通过一个安全的 Vsock 端口和 S3，cloudHSM 进行通信。
用户钱包私钥直接在 TEE 生成并保存到 S3。签名在 TEE 环境下进行。
热钱包私钥在 cloudHSM 中生成并保存到 cloudHSM 里。签名在 cloudHSM 环境下进行。
S3 和 cloudHSM 通过 Vsock 和 TEE 进行安全通信。

## 安全

- https
- cloudHSM 的 credentials（keyName，KeyPath）
- Bearer Token
- AesKey

