// 这个文件展示了如何改进 wallet-sign-go 项目中的错误处理
// 注意：这是示例代码，不能直接运行

package examples

import (
	"context"
	"errors"
	"fmt"
	"log/slog"
)

// ===== 1. 定义项目级别的错误类型 =====

// 基础错误类型
var (
	ErrInvalidInput     = errors.New("无效输入")
	ErrNotFound         = errors.New("未找到")
	ErrUnauthorized     = errors.New("未授权")
	ErrInternalError    = errors.New("内部错误")
	ErrCryptoOperation  = errors.New("加密操作失败")
	ErrDatabaseOperation = errors.New("数据库操作失败")
)

// SSM 错误类型
type SSMError struct {
	Operation  string // sign, verify, generate
	CryptoType string // ECDSA, EdDSA, RSA
	Field      string // privateKey, publicKey, message
	Cause      error
}

func (e *SSMError) Error() string {
	return fmt.Sprintf("SSM错误 [%s:%s:%s]: %v", 
		e.Operation, e.CryptoType, e.Field, e.Cause)
}

func (e *SSMError) Unwrap() error {
	return e.Cause
}

// RPC 错误类型
type RPCError struct {
	Code    int32
	Message string
	Details error
}

func (e *RPCError) Error() string {
	if e.Details != nil {
		return fmt.Sprintf("RPC错误 [%d]: %s - %v", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("RPC错误 [%d]: %s", e.Code, e.Message)
}

func (e *RPCError) Unwrap() error {
	return e.Details
}

// 数据库错误类型
type DatabaseError struct {
	Operation string // get, set, delete
	Key       string
	Cause     error
}

func (e *DatabaseError) Error() string {
	return fmt.Sprintf("数据库错误 [%s:%s]: %v", e.Operation, e.Key, e.Cause)
}

func (e *DatabaseError) Unwrap() error {
	return e.Cause
}

// ===== 2. 改进的 SSM 接口和实现 =====

// 改进的 ISsm 接口
type ISsm interface {
	GenerateKeyPair() (*Keypair, error)
	Sign(privateKeyHex string, messageHashHex string) (string, error)
	VerifySignature(publicKeyHex string, messageHashHex string, signatureHex string) (bool, error)
}

type Keypair struct {
	PrivateKey          string
	PublicKey           string
	CompressedPublicKey string
}

// 改进的 ECDSA 签名器
type EcdsaSigner struct{}

func (e *EcdsaSigner) Sign(privateKey string, messageHex string) (string, error) {
	// 参数验证
	if privateKey == "" {
		return "", &SSMError{
			Operation:  "sign",
			CryptoType: "ECDSA",
			Field:      "privateKey",
			Cause:      fmt.Errorf("%w: 私钥不能为空", ErrInvalidInput),
		}
	}

	if messageHex == "" {
		return "", &SSMError{
			Operation:  "sign",
			CryptoType: "ECDSA",
			Field:      "message",
			Cause:      fmt.Errorf("%w: 消息不能为空", ErrInvalidInput),
		}
	}

	// 解析私钥
	ecdsaPrivateKey, err := parseECDSAPrivateKey(privateKey)
	if err != nil {
		return "", &SSMError{
			Operation:  "sign",
			CryptoType: "ECDSA",
			Field:      "privateKey",
			Cause:      fmt.Errorf("解析私钥失败: %w", err),
		}
	}

	// 执行签名
	signature, err := performECDSASign(ecdsaPrivateKey, messageHex)
	if err != nil {
		return "", &SSMError{
			Operation:  "sign",
			CryptoType: "ECDSA",
			Field:      "signature",
			Cause:      fmt.Errorf("%w: %v", ErrCryptoOperation, err),
		}
	}

	return signature, nil
}

func (e *EcdsaSigner) VerifySignature(publicKeyHex string, messageHashHex string, signatureHex string) (bool, error) {
	// 参数验证
	if publicKeyHex == "" {
		return false, &SSMError{
			Operation:  "verify",
			CryptoType: "ECDSA",
			Field:      "publicKey",
			Cause:      fmt.Errorf("%w: 公钥不能为空", ErrInvalidInput),
		}
	}

	// 验证逻辑...
	isValid, err := performECDSAVerify(publicKeyHex, messageHashHex, signatureHex)
	if err != nil {
		return false, &SSMError{
			Operation:  "verify",
			CryptoType: "ECDSA",
			Field:      "verification",
			Cause:      fmt.Errorf("%w: %v", ErrCryptoOperation, err),
		}
	}

	return isValid, nil
}

// ===== 3. 改进的数据库接口 =====

type Database interface {
	Get(key string) (string, error)
	Set(key, value string) error
	Delete(key string) error
}

type LevelDB struct {
	// db implementation
}

func (db *LevelDB) Get(key string) (string, error) {
	if key == "" {
		return "", &DatabaseError{
			Operation: "get",
			Key:       key,
			Cause:     fmt.Errorf("%w: 键不能为空", ErrInvalidInput),
		}
	}

	// 执行数据库查询
	value, err := db.performGet(key)
	if err != nil {
		if isNotFoundError(err) {
			return "", &DatabaseError{
				Operation: "get",
				Key:       key,
				Cause:     fmt.Errorf("%w: 键不存在", ErrNotFound),
			}
		}
		return "", &DatabaseError{
			Operation: "get",
			Key:       key,
			Cause:     fmt.Errorf("%w: %v", ErrDatabaseOperation, err),
		}
	}

	return value, nil
}

func (db *LevelDB) Set(key, value string) error {
	if key == "" {
		return &DatabaseError{
			Operation: "set",
			Key:       key,
			Cause:     fmt.Errorf("%w: 键不能为空", ErrInvalidInput),
		}
	}

	if err := db.performSet(key, value); err != nil {
		return &DatabaseError{
			Operation: "set",
			Key:       key,
			Cause:     fmt.Errorf("%w: %v", ErrDatabaseOperation, err),
		}
	}

	return nil
}

// ===== 4. 改进的 RPC 服务 =====

type RpcServer struct {
	config *Config
	db     Database
	logger *slog.Logger
}

// 统一的错误处理方法
func (r *RpcServer) handleError(ctx context.Context, err error, operation string) *SignTxMessageResponse {
	// 记录详细错误日志
	r.logger.ErrorContext(ctx, "RPC操作失败",
		"operation", operation,
		"error", err.Error(),
		"error_type", fmt.Sprintf("%T", err),
	)

	// 根据错误类型返回适当的响应
	var rpcErr *RPCError
	if errors.As(err, &rpcErr) {
		return &SignTxMessageResponse{
			Code: rpcErr.Code,
			Msg:  rpcErr.Message,
		}
	}

	var ssmErr *SSMError
	if errors.As(err, &ssmErr) {
		return &SignTxMessageResponse{
			Code: 400, // Bad Request
			Msg:  "加密操作失败: " + ssmErr.Error(),
		}
	}

	var dbErr *DatabaseError
	if errors.As(err, &dbErr) {
		if errors.Is(dbErr.Cause, ErrNotFound) {
			return &SignTxMessageResponse{
				Code: 404, // Not Found
				Msg:  "未找到对应的密钥",
			}
		}
		return &SignTxMessageResponse{
			Code: 500, // Internal Server Error
			Msg:  "数据库操作失败",
		}
	}

	// 默认错误处理
	return &SignTxMessageResponse{
		Code: 500,
		Msg:  "内部服务器错误",
	}
}

func (r *RpcServer) SignTxMessage(ctx context.Context, request *SignTxMessageRequest) (*SignTxMessageResponse, error) {
	// 参数验证
	if request.PublicKey == "" {
		err := &RPCError{
			Code:    400,
			Message: "公钥不能为空",
		}
		return r.handleError(ctx, err, "SignTxMessage"), nil
	}

	if request.MessageHash == "" {
		err := &RPCError{
			Code:    400,
			Message: "消息哈希不能为空",
		}
		return r.handleError(ctx, err, "SignTxMessage"), nil
	}

	// 查找私钥
	privateKey, err := r.db.Get(request.PublicKey)
	if err != nil {
		return r.handleError(ctx, err, "SignTxMessage"), nil
	}

	// 执行签名
	signer := r.getSigner(request.Type)
	signature, err := signer.Sign(privateKey, request.MessageHash)
	if err != nil {
		return r.handleError(ctx, err, "SignTxMessage"), nil
	}

	// 记录成功日志
	r.logger.InfoContext(ctx, "签名操作成功",
		"crypto_type", request.Type.String(),
		"public_key_hash", hashString(request.PublicKey),
	)

	return &SignTxMessageResponse{
		Code: 200,
		Msg:  "签名成功",
		Data: signature,
	}, nil
}

// ===== 5. 辅助函数 =====

// 模拟函数，实际实现需要根据具体情况
func parseECDSAPrivateKey(privateKey string) (interface{}, error) {
	// 实际的 ECDSA 私钥解析逻辑
	return nil, nil
}

func performECDSASign(privateKey interface{}, message string) (string, error) {
	// 实际的 ECDSA 签名逻辑
	return "", nil
}

func performECDSAVerify(publicKey, message, signature string) (bool, error) {
	// 实际的 ECDSA 验证逻辑
	return false, nil
}

func (db *LevelDB) performGet(key string) (string, error) {
	// 实际的数据库查询逻辑
	return "", nil
}

func (db *LevelDB) performSet(key, value string) error {
	// 实际的数据库设置逻辑
	return nil
}

func isNotFoundError(err error) bool {
	// 检查是否为 not found 错误
	return false
}

func hashString(s string) string {
	// 对字符串进行哈希处理，用于日志记录
	return "hashed_" + s[:8] + "..."
}

// 模拟类型定义
type Config struct{}
type SignTxMessageRequest struct {
	Type        CryptoType
	PublicKey   string
	MessageHash string
}
type SignTxMessageResponse struct {
	Code int32
	Msg  string
	Data string
}
type CryptoType int

func (c CryptoType) String() string { return "ECDSA" }

func (r *RpcServer) getSigner(cryptoType CryptoType) ISsm {
	return &EcdsaSigner{}
}
