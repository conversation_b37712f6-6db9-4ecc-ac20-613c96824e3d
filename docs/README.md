# 文档目录

本目录包含了 `wallet-sign-go` 项目的相关文档。

## 文档列表

### 1. [Go 语言异常处理最佳实践](./go-error-handling-best-practices.md)
详细介绍了 Go 语言中错误处理的最佳实践，包括：
- 错误处理模式和原则
- 错误创建和传播
- 具体代码示例
- 针对当前项目的改进建议
- 工具和库推荐
- 性能考虑

### 2. [错误处理改进示例](./error-handling-improvements.go)
提供了针对当前 `wallet-sign-go` 项目的具体错误处理改进代码示例，包括：
- 自定义错误类型定义
- SSM 模块的错误处理改进
- 数据库操作的错误处理
- RPC 服务的统一错误处理

### 3. [什么是TEE](./什么是TEE.md)
介绍了可信执行环境（TEE）的相关概念。

### 4. [签名机 AWS 部署方案](./签名机%20AWS%20部署方案.md)
描述了在 AWS 环境下部署签名机的方案。

### 5. [TODO 列表](./todo.md)
项目的待办事项列表。

## 如何使用这些文档

### 对于开发者
1. 首先阅读 [Go 语言异常处理最佳实践](./go-error-handling-best-practices.md) 了解错误处理的理论基础
2. 参考 [错误处理改进示例](./error-handling-improvements.go) 中的代码来改进现有项目
3. 在实际开发中应用这些最佳实践

### 对于代码审查
使用文档中的最佳实践作为代码审查的标准：
- 检查是否正确处理了所有错误
- 验证错误信息是否有意义且包含足够的上下文
- 确保使用了适当的错误类型和包装方式

### 对于新团队成员
这些文档可以作为入门指南，帮助新成员快速了解项目的错误处理规范和最佳实践。

## 贡献指南

如果你想为这些文档做出贡献：
1. 确保内容准确且有用
2. 提供具体的代码示例
3. 保持文档的结构清晰
4. 及时更新过时的信息

## 反馈

如果你对这些文档有任何建议或发现了错误，请通过以下方式反馈：
- 创建 Issue
- 提交 Pull Request
- 直接联系维护者

---

*最后更新时间：2025-08-01*
