package main

import (
	"fmt"
	"github.com/ethereum/go-ethereum/log"
	"github.com/urfave/cli/v2"
	"wallet-sign-go/config"
	"wallet-sign-go/flags"
	"wallet-sign-go/service"
)

func NewCli() *cli.App {
	return &cli.App{
		Name:    "wallet-ssm-go",
		Version: "0.0.1",
		Commands: []*cli.Command{
			{
				Name:   "rpc",
				Action: runRpc,
				Flags:  flags.AllFlags,
			},
		},
	}
}

func runRpc(ctx *cli.Context) error {
	cfg := config.NewConfig(ctx)
	log.Info("config", "config", fmt.Sprintf("%+v", cfg))
	log.Info("Running RPC Server")
	server, err := service.NewRpcServer(cfg)
	if err != nil {
		return err
	}

	return server.Start()
}
