syntax = "proto3";
option go_package="./protobuf/wallet;pb_wallet";

service WalletService {
  rpc getSupportSignWay(GetSupportSignWayRequest) returns (GetSupportSignWayResponse) {}
  rpc exportPublicKeyList(ExportPublicKeyListRequest) returns (ExportPublicKeyListResponse) {}
  rpc signTxMessage(SignTxMessageRequest) returns (SignTxMessageResponse) {}
  rpc signTxMessageInBatches(SignTxMessageInBatchesRequest) returns (SignTxMessageInBatchesResponse) {}
}

enum EReturnCode {
  ERROR = 0;
  SUCCESS = 1;
}

enum ECryptoType {
  ECDSA = 0;
  EDDSA = 1;
  RSA = 2;
}

message GetSupportSignWayRequest {
}

message GetSupportSignWayResponse{
    EReturnCode Code =1;
    string Msg = 2;
    repeated string Data = 3;
}

message ExportPublicKeyListRequest {
  ECryptoType type = 1;
  uint64 Count = 2;
}

message ExportPublicKeyListResponse {
    EReturnCode Code = 1;
    string Msg = 2;
    repeated PublicKey Data = 3;
}

message PublicKey {
  string PublicKey = 1;
  string CompressedPublicKey = 2;
}

message SignTxMessageRequest {
  ECryptoType type = 1;
  string PublicKey = 2;
  string MessageHash = 3;
}

message SignTxMessageResponse {
  EReturnCode Code = 1;
  string Msg = 2;
  string Data = 3;
}

message SignTxMessageInBatchesRequest {
  repeated  SignTxMessageRequest list = 1;
}

message SignTxMessageInBatchesResponse {
  EReturnCode Code = 1;
  string Msg = 2;
  repeated SignTxMessageResponse data = 3;
}