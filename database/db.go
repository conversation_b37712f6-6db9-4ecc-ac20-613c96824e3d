package database

import (
	"github.com/ethereum/go-ethereum/log"
	"github.com/syndtr/goleveldb/leveldb"
	"wallet-sign-go/config"
)

var key = "sr"

type Database struct {
	db  *leveldb.DB
	aes *AESCrypto
}

func (d *Database) Store(k string, v string) error {
	encrypted, err := d.aes.Encrypt(v)
	if err != nil {
		return err
	}

	return d.db.Put(
		[]byte(k),
		[]byte(encrypted),
		nil,
	)
}

func (d *Database) Get(k string) string {
	v, err := d.db.Get([]byte(k), nil)

	if err != nil {
		log.Error("Get value from database failed", "err", err.Error(), "k", k)
		return ""
	}

	decrypt, err := d.aes.Decrypt(string(v))
	if err != nil {
		log.Error("Get value from database failed", "err", err.<PERSON><PERSON><PERSON>(), "k", k)
		return ""
	}

	return decrypt
}

func NewDB(config *config.Config) (*Database, error) {
	db, err := leveldb.OpenFile(config.DataLocation, nil)
	if err != nil {
		return nil, err
	}

	aes, err := NewAESCrypto(config.AesKey)
	if err != nil {
		return nil, err
	}
	return &Database{
		db:  db,
		aes: aes,
	}, nil
}
